/**
 * Progress Tracking Charts Widget
 * Purpose: Visual charts showing progress for 95+ components with real-time updates
 * Features: Category breakdown, completion trends, enhancement metrics
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Paper
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Assessment as ChartIcon,
  CheckCircle as CompletedIcon,
  AutoAwesome as EnhancedIcon
} from '@mui/icons-material';
import type { IImplementationProgressResponse } from '../../types/tracking.types';

interface ProgressTrackingChartsProps {
  data?: IImplementationProgressResponse;
  loading?: boolean;
  error?: Error | null;
}

interface CategoryStats {
  category: string;
  total: number;
  completed: number;
  enhanced: number;
  inProgress: number;
  notStarted: number;
  totalLOC: number;
  actualLOC: number;
  completionRate: number;
  enhancementRate: number;
}

export default function ProgressTrackingCharts({ data, loading, error }: ProgressTrackingChartsProps) {
  const [mounted, setMounted] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [categoryStats, setCategoryStats] = useState<CategoryStats[]>([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Calculate category statistics
  useEffect(() => {
    if (!data?.progress) return;

    const categories = ['governance', 'tracking', 'security', 'integration'];
    const stats: CategoryStats[] = [];

    categories.forEach(category => {
      const categoryComponents = data.progress.filter(p => p.category === category);
      const completed = categoryComponents.filter(p => p.status === 'completed' || p.status === 'enhanced').length;
      const enhanced = categoryComponents.filter(p => p.status === 'enhanced').length;
      const inProgress = categoryComponents.filter(p => p.status === 'in-progress').length;
      const notStarted = categoryComponents.filter(p => p.status === 'not-started').length;
      const totalLOC = categoryComponents.reduce((sum, p) => sum + p.plannedLOC, 0);
      const actualLOC = categoryComponents.reduce((sum, p) => sum + p.actualLOC, 0);

      stats.push({
        category: category.charAt(0).toUpperCase() + category.slice(1),
        total: categoryComponents.length,
        completed,
        enhanced,
        inProgress,
        notStarted,
        totalLOC,
        actualLOC,
        completionRate: totalLOC > 0 ? Math.round((actualLOC / totalLOC) * 100) : 0,
        enhancementRate: categoryComponents.length > 0 ? Math.round((enhanced / categoryComponents.length) * 100) : 0
      });
    });

    setCategoryStats(stats);
  }, [data]);

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load progress data: {error.message}
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress size={40} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading progress charts...
        </Typography>
      </Box>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No progress data available
      </Alert>
    );
  }

  const filteredStats = selectedCategory === 'all' 
    ? categoryStats 
    : categoryStats.filter(stat => stat.category.toLowerCase() === selectedCategory);

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header with Filter */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          <ChartIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Implementation Progress Charts
        </Typography>
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Category</InputLabel>
          <Select
            value={selectedCategory}
            label="Category"
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <MenuItem value="all">All Categories</MenuItem>
            <MenuItem value="governance">Governance</MenuItem>
            <MenuItem value="tracking">Tracking</MenuItem>
            <MenuItem value="security">Security</MenuItem>
            <MenuItem value="integration">Integration</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Total Components
                  </Typography>
                  <Typography variant="h4" component="div" color="primary.main">
                    {data.summary.totalComponents}
                  </Typography>
                </Box>
                <TrendingUpIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Completed
                  </Typography>
                  <Typography variant="h4" component="div" color="success.main">
                    {data.summary.completedComponents}
                  </Typography>
                </Box>
                <CompletedIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Enhanced
                  </Typography>
                  <Typography variant="h4" component="div" color="secondary.main">
                    {data.summary.enhancedComponents}
                  </Typography>
                </Box>
                <EnhancedIcon color="secondary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Overall Completion
                  </Typography>
                  <Typography variant="h4" component="div" color="info.main">
                    {data.summary.overallCompletion}%
                  </Typography>
                </Box>
                <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                  <CircularProgress
                    variant="determinate"
                    value={data.summary.overallCompletion}
                    size={40}
                    thickness={4}
                    color="info"
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Category Progress Charts */}
      <Grid container spacing={3}>
        {filteredStats.map((stat) => (
          <Grid item xs={12} md={6} key={stat.category}>
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" component="h3" gutterBottom>
                  {stat.category} Components
                </Typography>
                
                <Box mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Completion Progress
                    </Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {stat.completionRate}%
                    </Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={Math.min(stat.completionRate, 100)} 
                    sx={{ height: 8, borderRadius: 4 }}
                    color={stat.completionRate > 100 ? "secondary" : "primary"}
                  />
                </Box>

                <Box display="flex" gap={1} mb={2} flexWrap="wrap">
                  <Chip 
                    label={`${stat.completed} Completed`} 
                    color="success" 
                    size="small" 
                    variant="outlined"
                  />
                  <Chip 
                    label={`${stat.enhanced} Enhanced`} 
                    color="secondary" 
                    size="small" 
                    variant="outlined"
                  />
                  <Chip 
                    label={`${stat.inProgress} In Progress`} 
                    color="warning" 
                    size="small" 
                    variant="outlined"
                  />
                  {stat.notStarted > 0 && (
                    <Chip 
                      label={`${stat.notStarted} Not Started`} 
                      color="default" 
                      size="small" 
                      variant="outlined"
                    />
                  )}
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    LOC: {stat.actualLOC.toLocaleString()} / {stat.totalLOC.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Enhancement: {stat.enhancementRate}%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Last Update Info */}
      <Paper elevation={1} sx={{ p: 2, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="body2" color="text.secondary" textAlign="center">
          Last updated: {new Date(data.lastUpdate).toLocaleString()}
        </Typography>
      </Paper>
    </Box>
  );
}
